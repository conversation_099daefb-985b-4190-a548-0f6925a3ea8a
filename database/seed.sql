-- Insert sample benefits
INSERT INTO benefits (name, category, icon) VALUES
('Health Insurance', 'health', '🏥'),
('Dental Insurance', 'health', '🦷'),
('Vision Insurance', 'health', '👁️'),
('Mental Health Support', 'health', '🧠'),
('Gym Membership', 'wellness', '💪'),
('Wellsport', 'wellness', '🏃'),
('Flexible Working Hours', 'work_life', '⏰'),
('Remote Work', 'work_life', '🏠'),
('Sabbatical Leave', 'time_off', '🌴'),
('Unlimited PTO', 'time_off', '🏖️'),
('Parental Leave', 'time_off', '👶'),
('Stock Options', 'financial', '📈'),
('Retirement Plan', 'financial', '💰'),
('Learning Budget', 'development', '📚'),
('Conference Attendance', 'development', '🎤'),
('Free Lunch', 'other', '🍽️'),
('Company Car', 'other', '🚗'),
('Bike to Work Scheme', 'other', '🚲'),
('Pet-Friendly Office', 'other', '🐕'),
('Childcare Support', 'other', '👨‍👩‍👧‍👦');

-- Insert sample companies
INSERT INTO companies (name, location, size, industry, description, domain, verified) VALUES
('SAP', 'Walldorf, Germany', 'enterprise', 'Technology', 'Global leader in enterprise software solutions', 'sap.com', true),
('Deutsche Bank', 'Frankfurt, Germany', 'enterprise', 'Financial Services', 'Leading global investment bank and financial services company', 'db.com', true),
('Accenture', 'Dublin, Ireland', 'enterprise', 'Consulting', 'Global professional services company with leading capabilities in digital, cloud and security', 'accenture.com', true),
('PwC', 'London, UK', 'large', 'Consulting', 'One of the Big Four accounting firms providing audit, tax and consulting services', 'pwc.com', true),
('Commerzbank', 'Frankfurt, Germany', 'large', 'Financial Services', 'Major German commercial bank', 'commerzbank.de', true),
('DZ Bank', 'Frankfurt, Germany', 'large', 'Financial Services', 'Central institution for cooperative banks in Germany', 'dzbank.de', true),
('Lufthansa', 'Cologne, Germany', 'enterprise', 'Aviation', 'German airline and aviation group', 'lufthansa.com', true),
('Siemens', 'Munich, Germany', 'enterprise', 'Technology', 'Global technology company focused on industry, infrastructure, transport, and healthcare', 'siemens.com', true);

-- Insert sample company benefits (linking companies with benefits)
INSERT INTO company_benefits (company_id, benefit_id, is_verified) 
SELECT 
    c.id,
    b.id,
    true
FROM companies c
CROSS JOIN benefits b
WHERE 
    (c.name = 'SAP' AND b.name IN ('Health Insurance', 'Wellsport', 'Flexible Working Hours', 'Remote Work', 'Learning Budget', 'Stock Options')) OR
    (c.name = 'Deutsche Bank' AND b.name IN ('Health Insurance', 'Wellsport', 'Retirement Plan', 'Stock Options', 'Gym Membership', 'Free Lunch')) OR
    (c.name = 'Accenture' AND b.name IN ('Health Insurance', 'Wellsport', 'Learning Budget', 'Conference Attendance', 'Flexible Working Hours', 'Mental Health Support')) OR
    (c.name = 'PwC' AND b.name IN ('Health Insurance', 'Wellsport', 'Learning Budget', 'Flexible Working Hours', 'Parental Leave', 'Bike to Work Scheme')) OR
    (c.name = 'Commerzbank' AND b.name IN ('Health Insurance', 'Retirement Plan', 'Stock Options', 'Gym Membership', 'Parental Leave')) OR
    (c.name = 'DZ Bank' AND b.name IN ('Health Insurance', 'Retirement Plan', 'Flexible Working Hours', 'Learning Budget')) OR
    (c.name = 'Lufthansa' AND b.name IN ('Health Insurance', 'Company Car', 'Parental Leave', 'Retirement Plan', 'Free Lunch')) OR
    (c.name = 'Siemens' AND b.name IN ('Health Insurance', 'Learning Budget', 'Stock Options', 'Flexible Working Hours', 'Gym Membership', 'Mental Health Support'));
