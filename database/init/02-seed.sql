-- Insert sample benefits
INSERT INTO benefits (name, category, icon) VALUES
('Health Insurance', 'health', '🏥'),
('Dental Insurance', 'health', '🦷'),
('Vision Insurance', 'health', '👁️'),
('Mental Health Support', 'health', '🧠'),
('Gym Membership', 'wellness', '💪'),
('Wellsport', 'wellness', '🏃'),
('Flexible Working Hours', 'work_life', '⏰'),
('Remote Work', 'work_life', '🏠'),
('Sabbatical Leave', 'time_off', '🌴'),
('Unlimited PTO', 'time_off', '🏖️'),
('Parental Leave', 'time_off', '👶'),
('Stock Options', 'financial', '📈'),
('Retirement Plan', 'financial', '💰'),
('Learning Budget', 'development', '📚'),
('Conference Attendance', 'development', '🎤'),
('Free Lunch', 'other', '🍽️'),
('Company Car', 'other', '🚗'),
('Bike to Work Scheme', 'other', '🚲'),
('Pet-Friendly Office', 'other', '🐕'),
('Childcare Support', 'other', '👨‍👩‍👧‍👦');

-- Insert sample companies
INSERT INTO companies (name, location, size, industry, description, domain, verified) VALUES
('SAP', 'Walldorf, Germany', 'enterprise', 'Technology', 'Global leader in enterprise software solutions', 'sap.com', true),
('Deutsche Bank', 'Frankfurt, Germany', 'enterprise', 'Financial Services', 'Leading global investment bank and financial services company', 'db.com', true),
('Accenture', 'Dublin, Ireland', 'enterprise', 'Consulting', 'Global professional services company with leading capabilities in digital, cloud and security', 'accenture.com', true),
('PwC', 'London, UK', 'large', 'Consulting', 'One of the Big Four accounting firms providing audit, tax and consulting services', 'pwc.com', true),
('Commerzbank', 'Frankfurt, Germany', 'large', 'Financial Services', 'Major German commercial bank', 'commerzbank.de', true),
('DZ Bank', 'Frankfurt, Germany', 'large', 'Financial Services', 'Central institution for cooperative banks in Germany', 'dzbank.de', true),
('Lufthansa', 'Cologne, Germany', 'enterprise', 'Aviation', 'German airline and aviation group', 'lufthansa.com', true),
('Siemens', 'Munich, Germany', 'enterprise', 'Technology', 'Global technology company focused on industry, infrastructure, transport, and healthcare', 'siemens.com', true),
('TechStart GmbH', 'Berlin, Germany', 'startup', 'Technology', 'Innovative startup focusing on AI and machine learning solutions', 'techstart.de', false),
('FinanceFlow', 'Amsterdam, Netherlands', 'medium', 'Financial Services', 'Modern fintech company providing digital banking solutions', 'financeflow.com', false);

-- Insert sample company benefits (linking companies with benefits)
INSERT INTO company_benefits (company_id, benefit_id, is_verified) 
SELECT 
    c.id,
    b.id,
    true
FROM companies c
CROSS JOIN benefits b
WHERE 
    (c.name = 'SAP' AND b.name IN ('Health Insurance', 'Wellsport', 'Flexible Working Hours', 'Remote Work', 'Learning Budget', 'Stock Options')) OR
    (c.name = 'Deutsche Bank' AND b.name IN ('Health Insurance', 'Wellsport', 'Retirement Plan', 'Stock Options', 'Gym Membership', 'Free Lunch')) OR
    (c.name = 'Accenture' AND b.name IN ('Health Insurance', 'Wellsport', 'Learning Budget', 'Conference Attendance', 'Flexible Working Hours', 'Mental Health Support')) OR
    (c.name = 'PwC' AND b.name IN ('Health Insurance', 'Wellsport', 'Learning Budget', 'Flexible Working Hours', 'Parental Leave', 'Bike to Work Scheme')) OR
    (c.name = 'Commerzbank' AND b.name IN ('Health Insurance', 'Retirement Plan', 'Stock Options', 'Gym Membership', 'Parental Leave')) OR
    (c.name = 'DZ Bank' AND b.name IN ('Health Insurance', 'Retirement Plan', 'Flexible Working Hours', 'Learning Budget')) OR
    (c.name = 'Lufthansa' AND b.name IN ('Health Insurance', 'Company Car', 'Parental Leave', 'Retirement Plan', 'Free Lunch')) OR
    (c.name = 'Siemens' AND b.name IN ('Health Insurance', 'Learning Budget', 'Stock Options', 'Flexible Working Hours', 'Gym Membership', 'Mental Health Support')) OR
    (c.name = 'TechStart GmbH' AND b.name IN ('Health Insurance', 'Remote Work', 'Flexible Working Hours', 'Stock Options', 'Free Lunch')) OR
    (c.name = 'FinanceFlow' AND b.name IN ('Health Insurance', 'Wellsport', 'Remote Work', 'Learning Budget', 'Mental Health Support'));

-- Insert some unverified benefits for testing
INSERT INTO company_benefits (company_id, benefit_id, is_verified) 
SELECT 
    c.id,
    b.id,
    false
FROM companies c
CROSS JOIN benefits b
WHERE 
    (c.name = 'SAP' AND b.name IN ('Sabbatical Leave', 'Pet-Friendly Office')) OR
    (c.name = 'Deutsche Bank' AND b.name IN ('Unlimited PTO', 'Childcare Support')) OR
    (c.name = 'TechStart GmbH' AND b.name IN ('Sabbatical Leave', 'Unlimited PTO', 'Pet-Friendly Office')) OR
    (c.name = 'FinanceFlow' AND b.name IN ('Bike to Work Scheme', 'Conference Attendance'));

-- Insert sample users for testing (password: password123)
INSERT INTO users (email, password_hash, first_name, last_name, email_verified) VALUES
('<EMAIL>', '$2b$10$C/zV7Pk9QPT3y15XaPeHrugufDqFusJmuuFF7wcBMugBQEI2vZNPK', 'John', 'Doe', true),
('<EMAIL>', '$2b$10$C/zV7Pk9QPT3y15XaPeHrugufDqFusJmuuFF7wcBMugBQEI2vZNPK', 'Jane', 'Smith', true),
('<EMAIL>', '$2b$10$C/zV7Pk9QPT3y15XaPeHrugufDqFusJmuuFF7wcBMugBQEI2vZNPK', 'Mike', 'Wilson', true),
('<EMAIL>', '$2b$10$C/zV7Pk9QPT3y15XaPeHrugufDqFusJmuuFF7wcBMugBQEI2vZNPK', 'Sarah', 'Johnson', true),
('<EMAIL>', '$2b$10$C/zV7Pk9QPT3y15XaPeHrugufDqFusJmuuFF7wcBMugBQEI2vZNPK', 'Test', 'User', true);

-- Insert company users
INSERT INTO company_users (company_id, email, is_verified)
SELECT c.id, u.email, true
FROM companies c
JOIN users u ON (
    (c.domain = 'sap.com' AND u.email LIKE '%@sap.com') OR
    (c.domain = 'db.com' AND u.email LIKE '%@db.com') OR
    (c.domain = 'accenture.com' AND u.email LIKE '%@accenture.com') OR
    (c.domain = 'techstart.de' AND u.email LIKE '%@techstart.de')
);
