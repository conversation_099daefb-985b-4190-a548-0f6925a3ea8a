# Deployment Guide

This guide will help you deploy the Workwell application to production.

## Prerequisites

1. **Supabase Account**: Create a project at [supabase.com](https://supabase.com)
2. **Clerk Account**: Create an application at [clerk.com](https://clerk.com)
3. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)

## Step 1: Set up Supabase Database

1. Create a new Supabase project
2. Go to the SQL Editor in your Supabase dashboard
3. Run the schema from `database/schema.sql`
4. Optionally run the seed data from `database/seed.sql`
5. Note down your:
   - Project URL
   - Anon (public) key
   - Service role key (keep this secret)

## Step 2: Configure Clerk Authentication

1. Create a new Clerk application
2. Configure the following settings:
   - **Sign-in options**: Email
   - **Sign-up options**: Email
   - **Session settings**: Enable sessions
3. Set up redirect URLs:
   - Sign-in redirect: `/dashboard`
   - Sign-up redirect: `/dashboard`
   - After sign-out: `/`
4. Note down your:
   - Publishable key
   - Secret key

## Step 3: Deploy to Vercel

### Option A: Deploy via Vercel Dashboard

1. Connect your GitHub repository to Vercel
2. Import the project
3. Set the following environment variables:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key
NEXT_PUBLIC_APP_URL=https://your-app-domain.vercel.app
```

4. Deploy the application

### Option B: Deploy via Vercel CLI

1. Install Vercel CLI: `npm i -g vercel`
2. Login: `vercel login`
3. Deploy: `vercel`
4. Set environment variables: `vercel env add`

## Step 4: Configure Row Level Security (RLS)

After deployment, ensure your Supabase RLS policies are properly configured:

1. Go to Authentication > Policies in Supabase
2. Verify that the policies from `schema.sql` are active
3. Test the policies with your deployed application

## Step 5: Update Clerk Settings

1. Update your Clerk application settings with the production domain
2. Add your production domain to the allowed origins
3. Update redirect URLs to use your production domain

## Step 6: Test the Application

1. Visit your deployed application
2. Test user registration and login
3. Test company search and filtering
4. Test company verification workflow
5. Test benefit verification

## Environment Variables Reference

| Variable | Description | Required |
|----------|-------------|----------|
| `NEXT_PUBLIC_SUPABASE_URL` | Your Supabase project URL | Yes |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase anonymous key | Yes |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase service role key | Yes |
| `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` | Clerk publishable key | Yes |
| `CLERK_SECRET_KEY` | Clerk secret key | Yes |
| `NEXT_PUBLIC_APP_URL` | Your application URL | Yes |

## Troubleshooting

### Common Issues

1. **Authentication errors**: Check Clerk configuration and environment variables
2. **Database connection errors**: Verify Supabase credentials and RLS policies
3. **Build errors**: Check for TypeScript errors and missing dependencies

### Logs

- Check Vercel function logs for API errors
- Check browser console for client-side errors
- Check Supabase logs for database issues

## Monitoring

Consider setting up:
- Error tracking (Sentry)
- Analytics (PostHog, Plausible)
- Uptime monitoring
- Performance monitoring

## Security Checklist

- [ ] Environment variables are properly set
- [ ] RLS policies are enabled and tested
- [ ] Clerk authentication is properly configured
- [ ] HTTPS is enabled (automatic with Vercel)
- [ ] API routes are protected where necessary
- [ ] No sensitive data in client-side code
