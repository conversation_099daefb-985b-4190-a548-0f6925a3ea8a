{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "env": {"NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY": "@next_public_clerk_publishable_key", "CLERK_SECRET_KEY": "@clerk_secret_key", "NEXT_PUBLIC_APP_URL": "@next_public_app_url"}}