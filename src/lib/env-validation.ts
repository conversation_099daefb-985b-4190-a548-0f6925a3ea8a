import { logger } from './logger'

export interface EnvValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  config: Record<string, any>
}

export interface EnvVarConfig {
  name: string
  required: boolean
  defaultValue?: string
  validator?: (value: string) => boolean
  description?: string
  sensitive?: boolean // Don't log the actual value
}

// Environment variable configurations
const ENV_VARS: EnvVarConfig[] = [
  // Database
  {
    name: 'DATABASE_URL',
    required: true,
    description: 'PostgreSQL connection string',
    sensitive: true,
    validator: (value) => value.startsWith('postgresql://') || value.startsWith('postgres://')
  },
  
  // Redis
  {
    name: 'REDIS_URL',
    required: true,
    description: 'Redis connection string',
    sensitive: true,
    defaultValue: 'redis://localhost:6379',
    validator: (value) => value.startsWith('redis://') || value.startsWith('rediss://')
  },
  
  // Application
  {
    name: 'NEXT_PUBLIC_APP_URL',
    required: true,
    description: 'Public application URL',
    validator: (value) => value.startsWith('http://') || value.startsWith('https://')
  },
  
  {
    name: 'NODE_ENV',
    required: true,
    description: 'Node.js environment',
    defaultValue: 'development',
    validator: (value) => ['development', 'production', 'test'].includes(value)
  },
  
  // Email
  {
    name: 'SMTP_HOST',
    required: false,
    description: 'SMTP server hostname',
    defaultValue: 'localhost'
  },
  
  {
    name: 'SMTP_PORT',
    required: false,
    description: 'SMTP server port',
    defaultValue: '1025',
    validator: (value) => {
      const port = parseInt(value)
      return !isNaN(port) && port > 0 && port <= 65535
    }
  },
  
  {
    name: 'SMTP_USER',
    required: false,
    description: 'SMTP username',
    sensitive: true
  },
  
  {
    name: 'SMTP_PASS',
    required: false,
    description: 'SMTP password',
    sensitive: true
  },
  
  {
    name: 'FROM_EMAIL',
    required: false,
    description: 'Default from email address',
    defaultValue: '<EMAIL>',
    validator: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
  },
  
  // Authentication
  {
    name: 'USE_LOCAL_AUTH',
    required: false,
    description: 'Use local authentication instead of external providers',
    defaultValue: 'true',
    validator: (value) => ['true', 'false'].includes(value.toLowerCase())
  },
  
  {
    name: 'SESSION_SECRET',
    required: false,
    description: 'Secret for session encryption',
    sensitive: true
  },
  
  // Logging
  {
    name: 'LOG_LEVEL',
    required: false,
    description: 'Logging level',
    defaultValue: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    validator: (value) => ['error', 'warn', 'info', 'debug'].includes(value)
  },
  
  // Performance
  {
    name: 'ENABLE_PERFORMANCE_MONITORING',
    required: false,
    description: 'Enable performance monitoring',
    defaultValue: 'false',
    validator: (value) => ['true', 'false'].includes(value.toLowerCase())
  },
  
  // External services (optional)
  {
    name: 'NEXT_PUBLIC_SUPABASE_URL',
    required: false,
    description: 'Supabase project URL',
    validator: (value) => value.startsWith('https://')
  },
  
  {
    name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    required: false,
    description: 'Supabase anonymous key',
    sensitive: true
  },
  
  {
    name: 'SUPABASE_SERVICE_ROLE_KEY',
    required: false,
    description: 'Supabase service role key',
    sensitive: true
  }
]

export function validateEnvironment(): EnvValidationResult {
  const errors: string[] = []
  const warnings: string[] = []
  const config: Record<string, any> = {}
  
  logger.info('Starting environment validation')
  
  for (const envVar of ENV_VARS) {
    const value = process.env[envVar.name]
    
    // Check if required variable is missing
    if (envVar.required && !value) {
      errors.push(`Required environment variable ${envVar.name} is not set`)
      continue
    }
    
    // Use default value if not set
    const finalValue = value || envVar.defaultValue

    if (finalValue) {
      // Validate the value if validator is provided
      if (envVar.validator && !envVar.validator(finalValue)) {
        if (envVar.required) {
          errors.push(`Environment variable ${envVar.name} has invalid value`)
        } else {
          warnings.push(`Environment variable ${envVar.name} has invalid value`)
        }
        continue
      }
      
      // Store in config (mask sensitive values)
      config[envVar.name] = envVar.sensitive ? '[REDACTED]' : finalValue
      
      // Set default value in process.env if it wasn't set
      if (!value && envVar.defaultValue) {
        process.env[envVar.name] = envVar.defaultValue
        warnings.push(`Using default value for ${envVar.name}`)
      }
    } else if (!envVar.required) {
      warnings.push(`Optional environment variable ${envVar.name} is not set`)
    }
  }
  
  // Additional validation logic
  
  // Check for production-specific requirements
  if (process.env.NODE_ENV === 'production') {
    if (!process.env.SESSION_SECRET) {
      errors.push('SESSION_SECRET is required in production')
    }
    
    if (process.env.NEXT_PUBLIC_APP_URL?.includes('localhost')) {
      warnings.push('NEXT_PUBLIC_APP_URL should not use localhost in production')
    }
    
    if (process.env.DATABASE_URL?.includes('localhost')) {
      warnings.push('DATABASE_URL should not use localhost in production')
    }
  }
  
  // Check for development-specific warnings
  if (process.env.NODE_ENV === 'development') {
    if (!process.env.SMTP_HOST || process.env.SMTP_HOST === 'localhost') {
      warnings.push('Using localhost SMTP in development - emails will not be delivered externally')
    }
  }
  
  const isValid = errors.length === 0
  
  // Log results
  if (isValid) {
    logger.info('Environment validation passed', {
      warningCount: warnings.length,
      configuredVars: Object.keys(config).length
    })
    
    if (warnings.length > 0) {
      warnings.forEach(warning => logger.warn('Environment warning', { warning }))
    }
  } else {
    logger.error('Environment validation failed', {
      errorCount: errors.length,
      warningCount: warnings.length
    })
    
    errors.forEach(error => logger.error('Environment error', { error }))
  }
  
  return {
    isValid,
    errors,
    warnings,
    config
  }
}

// Validate environment on module load in production
if (process.env.NODE_ENV === 'production') {
  const result = validateEnvironment()
  if (!result.isValid) {
    logger.error('Environment validation failed, exiting', { errors: result.errors })
    process.exit(1)
  }
}

// Export for manual validation
export { ENV_VARS }

// If this file is run directly, validate environment
// eslint-disable-next-line @typescript-eslint/no-require-imports
if (require.main === module) {
  // Load environment variables from .env.local
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    require('dotenv').config({ path: '.env.local' })
  } catch (error) {
    console.warn('Could not load .env.local file:', error)
  }

  const result = validateEnvironment()

  console.log('\n=== Environment Validation Results ===')
  console.log(`Status: ${result.isValid ? '✅ VALID' : '❌ INVALID'}`)
  console.log(`Errors: ${result.errors.length}`)
  console.log(`Warnings: ${result.warnings.length}`)

  if (result.errors.length > 0) {
    console.log('\n❌ Errors:')
    result.errors.forEach(error => console.log(`  - ${error}`))
  }

  if (result.warnings.length > 0) {
    console.log('\n⚠️  Warnings:')
    result.warnings.forEach(warning => console.log(`  - ${warning}`))
  }

  console.log('\n📋 Configuration:')
  Object.entries(result.config).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`)
  })

  process.exit(result.isValid ? 0 : 1)
}
