'use client'

import { useState, useEffect } from 'react'
import { Header } from '@/components/header'
import { CompanyCard } from '@/components/company-card'
import { Button } from '@/components/ui/button'
import { Heart, Trash2 } from 'lucide-react'
import type { Company } from '@/types/database'

interface SavedCompany extends Company {
  saved_at: string
  benefit_count: number
}

export default function SavedCompaniesPage() {
  const [savedCompanies, setSavedCompanies] = useState<SavedCompany[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)

  useEffect(() => {
    checkAuthAndFetchCompanies()
  }, [])

  const checkAuthAndFetchCompanies = async () => {
    try {
      setIsLoading(true)

      // First check if user is authenticated
      const authResponse = await fetch('/api/auth/me')

      if (authResponse.ok) {
        setIsAuthenticated(true)
        // User is authenticated, fetch saved companies
        await fetchSavedCompanies()
      } else {
        setIsAuthenticated(false)
        setError('Please sign in to view your saved companies')
      }
    } catch (error) {
      console.error('Error checking authentication:', error)
      setIsAuthenticated(false)
      setError('Please sign in to view your saved companies')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchSavedCompanies = async () => {
    try {
      const response = await fetch('/api/saved-companies')

      if (response.ok) {
        const data = await response.json()
        setSavedCompanies(data)
        setError(null)
      } else if (response.status === 401) {
        setIsAuthenticated(false)
        setError('Your session has expired. Please sign in again.')
      } else {
        setError('Failed to load saved companies')
      }
    } catch (error) {
      console.error('Error fetching saved companies:', error)
      setError('Failed to load saved companies')
    }
  }

  const handleRemoveCompany = async (companyId: string) => {
    try {
      const response = await fetch(`/api/saved-companies?companyId=${companyId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setSavedCompanies(prev => prev.filter(company => company.id !== companyId))
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to remove company')
      }
    } catch (error) {
      console.error('Error removing company:', error)
      alert('Failed to remove company')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading your saved companies...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error && isAuthenticated === false) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <Heart className="w-16 h-16 text-gray-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Saved Companies</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="space-x-4">
              <Button onClick={() => window.location.href = '/sign-in'}>
                Sign In
              </Button>
              <Button variant="outline" onClick={() => window.location.href = '/sign-up'}>
                Create Account
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error && isAuthenticated === true) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <Heart className="w-16 h-16 text-gray-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Saved Companies</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="space-x-4">
              <Button onClick={checkAuthAndFetchCompanies}>
                Try Again
              </Button>
              <Button variant="outline" onClick={() => window.location.href = '/'}>
                Browse Companies
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-6 sm:py-8">
        <div className="text-center mb-8 sm:mb-12">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
            Saved Companies
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto px-2">
            Companies you&apos;ve saved for later review ({savedCompanies.length} total)
          </p>
        </div>

        {savedCompanies.length === 0 ? (
          <div className="text-center py-8 sm:py-12 px-4">
            <Heart className="w-12 h-12 sm:w-16 sm:h-16 text-gray-500 mx-auto mb-4" />
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">No saved companies yet</h2>
            <p className="text-sm sm:text-base text-gray-600 mb-6">
              Start exploring companies and save the ones that interest you!
            </p>
            <Button onClick={() => window.location.href = '/'}>
              Browse Companies
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 auto-rows-fr">
            {savedCompanies.map((company) => (
              <div key={company.id} className="relative group h-52 sm:h-56 overflow-hidden">
                <div className="h-full">
                  <CompanyCard company={company} variant="saved" />
                </div>
                <div className="absolute bottom-2 sm:bottom-3 right-2 sm:right-3 flex items-center gap-1 sm:gap-2 z-10">
                  <div className="bg-white rounded-full px-1.5 sm:px-2 py-0.5 sm:py-1 text-xs text-gray-600 shadow-sm">
                    {company.benefit_count} benefits
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleRemoveCompany(company.id)
                    }}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 bg-white shadow-md border-2 border-white rounded-full w-5 h-5 sm:w-6 sm:h-6 p-0 flex items-center justify-center"
                    title="Remove from saved"
                  >
                    <Trash2 className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>
    </div>
  )
}
