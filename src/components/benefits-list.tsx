'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Filter, ArrowRight } from 'lucide-react'
// Removed benefitToSlug import - using simple comma-separated approach
import type { Benefit, BenefitCategory } from '@/types/database'

interface CategoryOption {
  value: string
  label: string
  icon?: string
}

export function BenefitsList() {
  const [benefits, setBenefits] = useState<Benefit[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [categories, setCategories] = useState<CategoryOption[]>([])
  const router = useRouter()

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/benefit-categories')
      if (response.ok) {
        const data = await response.json()
        const categoryOptions: CategoryOption[] = [
          { value: 'all', label: 'All Benefits' },
          ...data.map((cat: BenefitCategory) => ({
            value: cat.name,
            label: cat.display_name,
            icon: cat.icon
          }))
        ]
        setCategories(categoryOptions)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      // Fallback to default categories
      setCategories([
        { value: 'all', label: 'All Benefits' },
        { value: 'health', label: 'Health & Medical' },
        { value: 'time_off', label: 'Time Off' },
        { value: 'financial', label: 'Financial' },
        { value: 'development', label: 'Development' },
        { value: 'wellness', label: 'Wellness' },
        { value: 'work_life', label: 'Work-Life Balance' },
        { value: 'other', label: 'Other' },
      ])
    }
  }

  useEffect(() => {
    if (categories.length > 0) {
      fetchBenefits()
    }
  }, [selectedCategory, categories])

  const fetchBenefits = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (selectedCategory !== 'all') {
        params.append('category', selectedCategory)
      }

      const response = await fetch(`/api/benefits?${params}`)
      if (response.ok) {
        const data = await response.json()
        setBenefits(data)
      }
    } catch (error) {
      console.error('Error fetching benefits:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredBenefits = benefits.filter(benefit => 
    selectedCategory === 'all' || benefit.category === selectedCategory
  )

  const benefitsByCategory = categories.reduce((acc, category) => {
    if (category.value === 'all') return acc

    acc[category.value] = benefits.filter(
      benefit => benefit.category === category.value
    )
    return acc
  }, {} as Record<string, Benefit[]>)

  const handleBenefitClick = (benefit: Benefit) => {
    // Navigate to homepage with benefit filter applied using simple comma-separated approach
    router.push(`/?benefits=${encodeURIComponent(benefit.name)}`)
  }

  return (
    <div className="space-y-8">
      {/* Category Filter */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center space-x-4 mb-4">
          <Filter className="w-5 h-5 text-gray-500" />
          <h2 className="text-lg font-semibold text-gray-900">Filter by Category</h2>
        </div>
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.value}
              onClick={() => setSelectedCategory(category.value)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedCategory === category.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.icon && `${category.icon} `}{category.label}
            </button>
          ))}
        </div>
      </div>

      {/* Benefits Display */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading benefits...</p>
        </div>
      ) : selectedCategory === 'all' ? (
        // Show all benefits grouped by category
        <div className="space-y-8">
          {Object.entries(benefitsByCategory).map(([category, categoryBenefits]) => {
            if (categoryBenefits.length === 0) return null
            
            const categoryInfo = categories.find(c => c.value === category)
            
            return (
              <div key={category} className="bg-white rounded-lg shadow-sm border p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {categoryInfo?.label}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {categoryBenefits.map((benefit) => (
                    <button
                      key={benefit.id}
                      onClick={() => handleBenefitClick(benefit)}
                      className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 text-left group cursor-pointer"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {benefit.icon && (
                            <span className="text-2xl">{benefit.icon}</span>
                          )}
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900 group-hover:text-blue-700">
                              {benefit.name}
                            </h4>
                            <p className="text-sm text-gray-600 capitalize">
                              {benefit.category.replace('_', ' ')}
                            </p>
                            {benefit.description && (
                              <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                {benefit.description}
                              </p>
                            )}
                          </div>
                        </div>
                        <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-blue-600 transition-colors" />
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      ) : (
        // Show filtered benefits
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            {categories.find(c => c.value === selectedCategory)?.label}
          </h3>
          {filteredBenefits.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredBenefits.map((benefit) => (
                <button
                  key={benefit.id}
                  onClick={() => handleBenefitClick(benefit)}
                  className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 text-left group cursor-pointer"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {benefit.icon && (
                        <span className="text-2xl">{benefit.icon}</span>
                      )}
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 group-hover:text-blue-700">
                          {benefit.name}
                        </h4>
                        <p className="text-sm text-gray-600 capitalize">
                          {benefit.category.replace('_', ' ')}
                        </p>
                        {benefit.description && (
                          <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                            {benefit.description}
                          </p>
                        )}
                      </div>
                    </div>
                    <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-blue-600 transition-colors" />
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <p className="text-gray-600">No benefits found in this category.</p>
          )}
        </div>
      )}
    </div>
  )
}
